"use client";

import React, { useState, useEffect } from "react";
import { GraduationCap, ExternalLink } from "lucide-react";
import { academyConfig } from "../config/academyConfig";

const AcademyButton = () => {
  const [isVisible, setIsVisible] = useState(true); // Set to true for immediate testing
  const [isHovered, setIsHovered] = useState(false);

  console.log("AcademyButton loading...", { isVisible, academyConfig });

  // Show button after a delay when page loads
  useEffect(() => {
    console.log("Setting up timer with delay:", academyConfig.showDelay);
    const timer = setTimeout(() => {
      console.log("Timer fired, showing button");
      setIsVisible(true);
    }, academyConfig.showDelay);

    return () => clearTimeout(timer);
  }, []);

  const handleAcademyClick = () => {
    console.log("Academy button clicked! Opening URL:", academyConfig.academyUrl);
    // Open the academy website URL
    window.open(academyConfig.academyUrl, "_blank", "noopener,noreferrer");
  };

  console.log("Rendering button, isVisible:", isVisible);

  if (!isVisible) {
    console.log("Button not visible, returning null");
    return null;
  }

  return (
    <div className="fixed bottom-8 left-8 z-[9999]">
      <button
        onClick={handleAcademyClick}
        className="bg-gradient-to-r from-[#100562] via-blue-600 to-[#100562] text-white px-4 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Icon */}
        <GraduationCap className="w-4 h-4" />

        {/* Text */}
        <div className="flex flex-col items-start">
          <span className="text-xs font-bold text-[#FFE300] uppercase tracking-wide">
            {academyConfig.text.primary}
          </span>
          <span className="text-sm font-bold leading-tight">
            {academyConfig.text.secondary}
          </span>
        </div>

        {/* External Link Icon */}
        <ExternalLink className="w-3 h-3 opacity-80" />
      </button>

      {/* Tooltip */}
      {isHovered && (
        <div className="absolute bottom-full left-0 mb-2 bg-black/80 text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap backdrop-blur-sm">
          {academyConfig.text.tooltip}
          <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80" />
        </div>
      )}
    </div>
  );
};

export default AcademyButton;
