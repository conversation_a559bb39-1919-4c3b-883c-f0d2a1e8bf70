# Professional Academy Website Redirect Card

A sophisticated, card-based sticky component that promotes your academy website with professional design and engaging user experience. The card appears on all pages with smooth animations and comprehensive information display.

## Features

- ✨ **Professional Card Design**: Glass-morphism effect with gradient borders
- 📊 **Information Rich**: Displays features, statistics, and trust indicators
- 📱 **Responsive Design**: Optimized for both desktop and mobile devices
- 🎨 **Highly Customizable**: Easy configuration through a comprehensive config file
- 🔄 **Smart Auto-show**: Appears automatically after a configurable delay
- 💾 **User Preference Memory**: Remembers if user dismissed it (localStorage)
- 🎯 **Accessible**: Includes proper ARIA labels and keyboard navigation
- 💫 **Advanced Animations**: Shimmer effects, micro-interactions, and smooth transitions
- 📈 **Analytics Ready**: Built-in Google Analytics event tracking
- 🏆 **Trust Building**: Shows ratings, student count, and availability

## Installation

The Academy card has been automatically integrated into your website and will appear on all pages.

## Configuration

You can customize the card by editing the comprehensive configuration file:

**File:** `src/config/academyConfig.js`

```javascript
export const academyConfig = {
  // Academy website URL - Update this with your actual academy website URL
  academyUrl: "https://academy.sipherweb.com",

  // Card appearance delay (in milliseconds)
  showDelay: 3000,

  // Card text configuration
  text: {
    primary: "Sipher Academy",
    secondary: "Professional Learning",
    tooltip: "Advance your career with expert-led courses"
  },

  // Features displayed in the card
  features: {
    courses: {
      icon: "BookOpen",
      label: "Courses",
      description: "Expert-led training"
    },
    certification: {
      icon: "Award",
      label: "Certified",
      description: "Industry recognized"
    },
    growth: {
      icon: "TrendingUp",
      label: "Growth",
      description: "Career advancement"
    }
  },

  // Statistics displayed
  stats: {
    rating: "4.9",
    students: "1000+",
    availability: "24/7"
  },

  // Analytics tracking
  analytics: {
    enabled: true,
    eventCategory: "Academy Button",
    eventLabel: "Academy Redirect"
  }
};
```

### Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `academyUrl` | The URL to redirect to when clicked | `"https://academy.sipherweb.com"` |
| `showDelay` | Delay before showing the card (ms) | `3000` |
| `text.primary` | Card header title | `"Sipher Academy"` |
| `text.secondary` | Card subtitle | `"Professional Learning"` |
| `text.tooltip` | Tooltip text on hover | `"Advance your career with expert-led courses"` |
| `stats.rating` | Academy rating display | `"4.9"` |
| `stats.students` | Student count display | `"1000+"` |
| `stats.availability` | Availability display | `"24/7"` |
| `analytics.enabled` | Enable Google Analytics tracking | `true` |

## Positioning

The button is positioned at the bottom-left corner of the screen:
- **Mobile**: 1rem from left edge, 2rem from bottom
- **Desktop**: 2rem from left edge, 2rem from bottom

This positioning ensures it doesn't conflict with the existing LiveChat button (bottom-right).

## Styling

The button uses your existing design system:
- Primary colors: `#100562` (dark blue) and `#FFE300` (yellow)
- Gradient backgrounds matching your brand
- Consistent with your website's visual language

## Browser Support

- Modern browsers with CSS Grid and Flexbox support
- Mobile browsers (iOS Safari, Chrome Mobile, etc.)
- Requires JavaScript enabled

## Performance

- Lightweight component (~2KB gzipped)
- Uses efficient animations with Framer Motion
- Lazy-loaded after initial page load
- No impact on Core Web Vitals

## Customization Examples

### Change the URL
```javascript
academyUrl: "https://learn.yourcompany.com"
```

### Modify the text
```javascript
text: {
  primary: "Join Our",
  secondary: "Learning Platform",
  tooltip: "Start learning today!"
}
```

### Adjust timing
```javascript
showDelay: 5000  // Show after 5 seconds
```

## Troubleshooting

### Button not appearing
1. Check that JavaScript is enabled
2. Verify the component is imported in `App.jsx`
3. Check browser console for errors

### Wrong URL opening
1. Update `academyUrl` in the config file
2. Ensure the URL includes `https://`

### Styling issues
1. Check that Tailwind CSS is properly configured
2. Verify Framer Motion is installed
3. Clear browser cache

## Files Modified

- `src/components/AcademyButton.jsx` - Main component
- `src/layout/AcademyButton.jsx` - Layout wrapper
- `src/config/academyConfig.js` - Configuration file
- `src/App.jsx` - Integration point

## Support

For customization help or issues, refer to the component code or contact your development team.
